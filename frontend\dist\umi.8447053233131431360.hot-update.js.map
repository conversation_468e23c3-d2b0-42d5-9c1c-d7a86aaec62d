{"version": 3, "sources": ["umi.8447053233131431360.hot-update.js", "src/app.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='6605424396301513257';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';\nimport { history } from '@umijs/max';\nimport {\n  ErrorBoundary,\n  Footer,\n  MessageProvider,\n} from '@/components';\nimport UserFloatButton from '@/components/FloatButton';\nimport { AuthService, UserService, TeamService } from '@/services';\nimport type { UserProfileResponse, TeamDetailResponse } from '@/types/api';\nimport { hasTeamInCurrentToken } from '@/utils/tokenUtils';\n\n\n\n/**\n * 全局初始化数据配置函数\n *\n * 这是UmiJS应用的核心初始化函数，负责在应用启动时设置全局状态。\n * 主要用于Layout组件的用户信息和权限初始化，是整个身份验证系统的入口点。\n *\n * 主要功能：\n * 1. 检查用户登录状态并获取用户信息\n * 2. 根据Token状态获取团队信息\n * 3. 提供数据获取函数供组件使用\n * 4. 处理初始化过程中的各种异常情况\n * 5. 为路由守卫提供必要的状态信息\n *\n * 初始化流程：\n * 1. 定义用户信息和团队信息的获取函数\n * 2. 检查当前路径是否需要身份验证\n * 3. 验证用户登录状态\n * 4. 获取用户基本信息\n * 5. 根据Token中的团队信息获取团队详情\n * 6. 返回初始化状态供全局使用\n *\n * 状态管理：\n * - currentUser：当前登录用户的详细信息\n * - currentTeam：当前选择团队的详细信息\n * - fetchUserInfo：重新获取用户信息的函数\n * - fetchTeamInfo：重新获取团队信息的函数\n *\n * 错误处理策略：\n * - Token过期：自动清除本地Token，返回未登录状态\n * - 网络错误：记录日志但不阻止应用启动\n * - 权限错误：返回安全的默认状态\n *\n * 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate\n */\nexport async function getInitialState(): Promise<{\n  currentUser?: UserProfileResponse;\n  currentTeam?: TeamDetailResponse;\n  loading?: boolean;\n  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;\n  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;\n}> {\n  /**\n   * 获取用户信息的函数\n   *\n   * 从后端API获取当前登录用户的详细信息，包括用户ID、邮箱、姓名等。\n   * 这个函数会被存储在全局状态中，供组件在需要时调用。\n   *\n   * 错误处理：\n   * - Token过期或无效：自动清除本地Token\n   * - 网络错误：返回undefined，让调用方处理\n   * - 权限错误：记录日志并返回undefined\n   *\n   * @returns Promise<UserProfileResponse | undefined> 用户信息或undefined\n   */\n  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {\n    try {\n      // 调用用户服务获取当前用户的详细信息\n      return await UserService.getUserProfile();\n    } catch (error) {\n      // 如果获取用户信息失败，可能是Token过期或无效\n      // 清除本地Token，让用户重新登录\n      AuthService.clearToken();\n      return undefined;\n    }\n  };\n\n  /**\n   * 获取团队信息的函数\n   *\n   * 从后端API获取当前选择团队的详细信息，包括团队名称、成员数量等。\n   * 只有在Token包含团队信息时才会尝试获取，避免不必要的API调用。\n   *\n   * 团队状态检查：\n   * - 检查Token中是否包含teamId\n   * - 只有团队Token才会调用团队API\n   * - 用户Token不会触发团队信息获取\n   *\n   * @returns Promise<TeamDetailResponse | undefined> 团队信息或undefined\n   */\n  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {\n    try {\n      // 检查当前Token是否包含团队信息\n      // 只有在用户已选择团队的情况下才尝试获取团队详情\n      if (!hasTeamInCurrentToken()) {\n        return undefined;\n      }\n\n      // 调用团队服务获取当前团队的详细信息\n      return await TeamService.getCurrentTeamDetail();\n    } catch (error) {\n      // 团队信息获取失败不影响用户基本功能\n      // 返回undefined，让组件显示无团队状态\n      return undefined;\n    }\n  };\n\n\n\n  /**\n   * 路径检查和初始化逻辑\n   *\n   * 根据当前访问的路径决定是否需要进行身份验证初始化。\n   * 对于公开页面（如登录、注册），跳过身份验证检查。\n   * 对于需要身份验证的页面，执行完整的初始化流程。\n   */\n  const { location } = history;\n\n  // 检查当前路径是否为公开页面（不需要身份验证）\n  if (!['/user/login', '/user/register'].includes(location.pathname)) {\n    try {\n      /**\n       * 身份验证检查\n       *\n       * 首先检查用户是否已登录（本地是否有有效Token）。\n       * 如果未登录，返回基础状态，让路由守卫处理跳转。\n       */\n      if (!AuthService.isLoggedIn()) {\n        // 用户未登录，返回基础状态\n        // 路由守卫会检测到currentUser为空并跳转到登录页\n        return {\n          fetchUserInfo,\n          fetchTeamInfo,\n        };\n      }\n\n      /**\n       * 用户信息获取\n       *\n       * 用户已登录，尝试获取用户的详细信息。\n       * 这一步会验证Token的有效性，如果Token过期会自动清除。\n       */\n      const currentUser = await fetchUserInfo();\n      if (!currentUser) {\n        // 用户信息获取失败，可能是Token过期\n        // 返回基础状态，让路由守卫处理重新登录\n        return {\n          fetchUserInfo,\n          fetchTeamInfo,\n        };\n      }\n\n      /**\n       * 团队信息获取\n       *\n       * 用户信息获取成功后，尝试获取团队信息。\n       * 只有在Token包含团队信息时才会执行，避免不必要的API调用。\n       * 团队信息获取失败不影响用户基本功能的使用。\n       */\n      const currentTeam = await fetchTeamInfo();\n\n      /**\n       * 返回完整的初始化状态\n       *\n       * 包含用户信息、团队信息（如果有）和数据获取函数。\n       * 这些信息会被存储在全局状态中，供整个应用使用。\n       */\n      return {\n        fetchUserInfo,\n        fetchTeamInfo,\n        currentUser,\n        currentTeam,\n      };\n    } catch (error) {\n      /**\n       * 初始化异常处理\n       *\n       * 捕获初始化过程中的所有异常，确保应用能够正常启动。\n       * 即使初始化失败，也要返回基础状态，让用户能够重新登录。\n       */\n      return {\n        fetchUserInfo,\n        fetchTeamInfo,\n      };\n    }\n  }\n\n  /**\n   * 公开页面的默认状态\n   *\n   * 对于登录、注册等公开页面，不需要进行身份验证检查。\n   * 只返回数据获取函数，供页面在需要时使用。\n   */\n  return {\n    fetchUserInfo,\n    fetchTeamInfo,\n  };\n}\n\n/**\n * ProLayout 布局配置\n *\n * 这是UmiJS应用的布局配置函数，定义了整个应用的UI布局和行为。\n * 主要负责页面布局、路由守卫、用户状态显示等功能。\n *\n * 主要功能：\n * 1. 页面布局和样式配置\n * 2. 路由切换时的身份验证检查\n * 3. 用户信息的显示（如水印）\n * 4. 全局组件的渲染（如错误边界、消息提供者）\n * 5. 自定义页面元素的配置\n *\n * 路由守卫机制：\n * - 在每次页面切换时检查用户登录状态\n * - 未登录用户自动跳转到登录页面\n * - 公开页面（登录、注册）允许匿名访问\n *\n * ProLayout 支持的API: https://procomponents.ant.design/components/layout\n */\nexport const layout: RunTimeLayoutConfig = ({ initialState }: any) => {\n  return {\n    /**\n     * 水印配置\n     *\n     * 在页面上显示用户名水印，用于标识当前登录用户。\n     * 只有在用户已登录时才显示水印。\n     */\n    waterMarkProps: {\n      content: initialState?.currentUser?.name,\n    },\n\n    /**\n     * 底部版权信息\n     *\n     * 渲染应用底部的版权信息和相关链接。\n     */\n    footerRender: () => <Footer />,\n\n    /**\n     * 右侧内容渲染\n     *\n     * 移除默认的右侧内容（如用户头像、设置等），\n     * 使用自定义的FloatButton组件替代。\n     */\n    rightContentRender: false,\n\n    /**\n     * 页面切换时的路由守卫\n     *\n     * 这是应用级别的路由守卫，在每次页面切换时执行身份验证检查。\n     * 确保只有已登录的用户才能访问需要身份验证的页面。\n     *\n     * 守卫逻辑：\n     * 1. 检查全局状态中是否有用户信息\n     * 2. 检查当前路径是否为公开页面\n     * 3. 未登录用户访问受保护页面时自动跳转到登录页\n     *\n     * 公开页面列表：\n     * - /user/login：用户登录页面\n     * - /user/register：用户注册页面\n     */\n    onPageChange: () => {\n      const { location } = history;\n\n      // 检查用户是否已登录且当前页面是否需要身份验证\n      if (\n        !initialState?.currentUser &&\n        !['/user/login', '/user/register'].includes(location.pathname)\n      ) {\n        // 用户未登录且访问受保护页面，跳转到登录页\n        history.push('/user/login');\n      }\n    },\n    /**\n     * 背景图片配置\n     *\n     * 自定义布局区域的背景装饰图片，用于美化页面视觉效果。\n     * 这些图片会作为背景元素显示在页面的不同位置。\n     */\n    bgLayoutImgList: [\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',\n        left: 85,\n        bottom: 100,\n        height: '303px',\n      },\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',\n        bottom: -68,\n        right: -45,\n        height: '303px',\n      },\n      {\n        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',\n        bottom: 0,\n        left: 0,\n        width: '331px',\n      },\n    ],\n\n    /**\n     * 自定义链接配置\n     *\n     * 页面底部或侧边栏的自定义链接列表。\n     * 当前为空数组，表示不显示额外的链接。\n     */\n    links: [],\n\n    /**\n     * 菜单头部渲染\n     *\n     * 自定义侧边栏菜单的头部内容。\n     * 设置为undefined表示使用默认的菜单头部。\n     */\n    menuHeaderRender: undefined,\n\n    /**\n     * 自定义403权限不足页面\n     *\n     * 当用户访问没有权限的页面时显示的内容。\n     * 当前被注释掉，使用系统默认的403页面。\n     */\n    // unAccessible: <div>unAccessible</div>,\n\n    /**\n     * 子组件渲染器\n     *\n     * 这是整个应用的根组件包装器，为所有页面提供全局功能。\n     * 包含错误边界、消息提供者和全局浮动按钮等功能。\n     *\n     * 全局组件说明：\n     * - ErrorBoundary：捕获和处理React组件错误\n     * - MessageProvider：提供全局消息通知功能\n     * - UserFloatButton：用户操作的浮动按钮（登录后显示）\n     *\n     * 这些组件确保了应用的稳定性和用户体验的一致性。\n     */\n    childrenRender: (children: any) => {\n      return (\n        <ErrorBoundary>\n          <MessageProvider>\n            {children}\n            {/* 全局 FloatButton - 在用户登录后显示 */}\n            <UserFloatButton />\n          </MessageProvider>\n        </ErrorBoundary>\n      );\n    },\n\n    /**\n     * 设置配置\n     *\n     * 移除settings属性，使用ProLayout的默认配置。\n     * 这简化了配置并使用了推荐的默认设置。\n     */\n  };\n};\n\n/**\n * UmiJS 请求配置\n *\n * 这是UmiJS应用的HTTP请求配置，定义了全局的请求行为和错误处理策略。\n * 与utils/request.ts中的自定义请求实例配合使用，提供完整的请求管理。\n *\n * 配置策略：\n * 1. 使用默认配置，保持简洁性\n * 2. 错误处理委托给utils/request.ts中的拦截器\n * 3. 身份验证由请求拦截器自动处理\n * 4. 响应处理由响应拦截器统一管理\n *\n * 架构设计：\n * - app.tsx：定义UmiJS级别的请求配置\n * - utils/request.ts：实现具体的请求逻辑和拦截器\n * - services/*：使用配置好的请求实例进行API调用\n *\n * 这种分层设计确保了请求处理的一致性和可维护性。\n *\n * 更多信息见文档：https://umijs.org/docs/max/request#配置\n */\nexport const request: RequestConfig = {\n  // 使用默认配置，所有复杂的请求处理逻辑都在utils/request.ts中实现\n  // 这包括：Token注入、错误处理、响应拦截、消息提示等\n};\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBC6CS,eAAe;2BAAf;;gBA8KT,MAAM;2BAAN;;gBAgKA,OAAO;2BAAP;;;;;;;wCA7XW;+CAKjB;yFACqB;6CAC0B;+CAEhB;;;;;;;;;YAsC/B,eAAe;gBAOpB;;;;;;;;;;;;GAYC,GACD,MAAM,gBAAgB;oBACpB,IAAI;wBACF,oBAAoB;wBACpB,OAAO,MAAM,qBAAW,CAAC,cAAc;oBACzC,EAAE,OAAO,OAAO;wBACd,2BAA2B;wBAC3B,oBAAoB;wBACpB,qBAAW,CAAC,UAAU;wBACtB,OAAO;oBACT;gBACF;gBAEA;;;;;;;;;;;;GAYC,GACD,MAAM,gBAAgB;oBACpB,IAAI;wBACF,oBAAoB;wBACpB,0BAA0B;wBAC1B,IAAI,CAAC,IAAA,iCAAqB,KACxB,OAAO;wBAGT,oBAAoB;wBACpB,OAAO,MAAM,qBAAW,CAAC,oBAAoB;oBAC/C,EAAE,OAAO,OAAO;wBACd,oBAAoB;wBACpB,yBAAyB;wBACzB,OAAO;oBACT;gBACF;gBAIA;;;;;;GAMC,GACD,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;gBAE5B,yBAAyB;gBACzB,IAAI,CAAC;oBAAC;oBAAe;iBAAiB,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAC/D,IAAI;oBACF;;;;;OAKC,GACD,IAAI,CAAC,qBAAW,CAAC,UAAU,IACzB,eAAe;oBACf,+BAA+B;oBAC/B,OAAO;wBACL;wBACA;oBACF;oBAGF;;;;;OAKC,GACD,MAAM,cAAc,MAAM;oBAC1B,IAAI,CAAC,aACH,sBAAsB;oBACtB,qBAAqB;oBACrB,OAAO;wBACL;wBACA;oBACF;oBAGF;;;;;;OAMC,GACD,MAAM,cAAc,MAAM;oBAE1B;;;;;OAKC,GACD,OAAO;wBACL;wBACA;wBACA;wBACA;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd;;;;;OAKC,GACD,OAAO;wBACL;wBACA;oBACF;gBACF;gBAGF;;;;;GAKC,GACD,OAAO;oBACL;oBACA;gBACF;YACF;YAsBO,MAAM,SAA8B,CAAC,EAAE,YAAY,EAAO;oBASlD;gBARb,OAAO;oBACL;;;;;KAKC,GACD,gBAAgB;wBACd,OAAO,EAAE,yBAAA,oCAAA,4BAAA,aAAc,WAAW,cAAzB,gDAAA,0BAA2B,IAAI;oBAC1C;oBAEA;;;;KAIC,GACD,cAAc,kBAAM,2BAAC,kBAAM;;;;;oBAE3B;;;;;KAKC,GACD,oBAAoB;oBAEpB;;;;;;;;;;;;;;KAcC,GACD,cAAc;wBACZ,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAO;wBAE5B,yBAAyB;wBACzB,IACE,EAAC,yBAAA,mCAAA,aAAc,WAAW,KAC1B,CAAC;4BAAC;4BAAe;yBAAiB,CAAC,QAAQ,CAAC,SAAS,QAAQ,GAE7D,uBAAuB;wBACvB,YAAO,CAAC,IAAI,CAAC;oBAEjB;oBACA;;;;;KAKC,GACD,iBAAiB;wBACf;4BACE,KAAK;4BACL,MAAM;4BACN,QAAQ;4BACR,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,QAAQ;4BACR,OAAO;4BACP,QAAQ;wBACV;wBACA;4BACE,KAAK;4BACL,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;qBACD;oBAED;;;;;KAKC,GACD,OAAO,EAAE;oBAET;;;;;KAKC,GACD,kBAAkB;oBAElB;;;;;KAKC,GACD,yCAAyC;oBAEzC;;;;;;;;;;;;KAYC,GACD,gBAAgB,CAAC;wBACf,qBACE,2BAAC,yBAAa;sCACZ,cAAA,2BAAC,2BAAe;;oCACb;kDAED,2BAAC,oBAAe;;;;;;;;;;;;;;;;oBAIxB;gBAQF;YACF;YAuBO,MAAM,UAAyB;YAGtC;;;;;;;;;;;;;;;;;;;;;;;ID9Xc;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC7kB"}