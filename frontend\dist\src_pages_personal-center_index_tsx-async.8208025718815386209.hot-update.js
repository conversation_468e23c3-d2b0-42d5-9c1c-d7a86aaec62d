globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/TodoManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _todo = __mako_require__("src/services/todo.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TabPane } = _antd.Tabs;
            const TodoManagement = (props)=>{
                _s();
                // TODO数据状态管理
                const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
                const [todoStats, setTodoStats] = (0, _react.useState)({
                    highPriorityCount: 0,
                    mediumPriorityCount: 0,
                    lowPriorityCount: 0,
                    totalCount: 0,
                    completedCount: 0,
                    completionPercentage: 0
                });
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                // 待办事项状态管理
                const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
                const [todoForm] = _antd.Form.useForm();
                const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
                // 过滤器状态
                const [activeTab, setActiveTab] = (0, _react.useState)('pending');
                const [searchText, setSearchText] = (0, _react.useState)('');
                // 获取TODO数据
                (0, _react.useEffect)(()=>{
                    const fetchTodoData = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            console.log('TodoManagement: 开始获取TODO数据');
                            // 分别获取TODO列表和统计数据，避免一个失败影响另一个
                            const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                                console.error('获取TODO列表失败:', error);
                                return [];
                            });
                            const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                                console.error('获取TODO统计失败:', error);
                                return {
                                    highPriorityCount: 0,
                                    mediumPriorityCount: 0,
                                    lowPriorityCount: 0,
                                    totalCount: 0,
                                    completedCount: 0,
                                    completionPercentage: 0
                                };
                            });
                            const [todos, stats] = await Promise.all([
                                todosPromise,
                                statsPromise
                            ]);
                            console.log('TodoManagement: 获取到TODO列表:', todos);
                            console.log('TodoManagement: 获取到统计数据:', stats);
                            setPersonalTasks(todos);
                            setTodoStats(stats);
                        } catch (error) {
                            console.error('获取TODO数据时发生未知错误:', error);
                            setError('获取TODO数据失败，请刷新页面重试');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTodoData();
                }, []);
                // 根据激活的标签和搜索文本过滤任务
                const filteredPersonalTasks = (personalTasks || []).filter((task)=>{
                    // 根据标签过滤
                    if (activeTab === 'pending' && task.status === 1) return false;
                    if (activeTab === 'completed' && task.status === 0) return false;
                    // 根据搜索文本过滤
                    if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
                    return true;
                });
                // 处理待办事项操作
                const handleToggleTodoStatus = async (id)=>{
                    try {
                        const task = personalTasks.find((t)=>t.id === id);
                        if (!task) return;
                        const newStatus = task.status === 0 ? 1 : 0;
                        await _todo.TodoService.updateTodo(id, {
                            status: newStatus
                        });
                        // 更新本地状态
                        setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                                ...task,
                                status: newStatus
                            } : task));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleAddOrUpdateTodo = async (values)=>{
                    try {
                        if (editingTodoId) {
                            // 更新现有待办事项
                            const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                        } else {
                            // 添加新待办事项
                            const newTodo = await _todo.TodoService.createTodo({
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks([
                                newTodo,
                                ...personalTasks
                            ]);
                        }
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                        // 重置表单并关闭模态框
                        setTodoModalVisible(false);
                        setEditingTodoId(null);
                        todoForm.resetFields();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleDeleteTodo = async (id)=>{
                    try {
                        console.log('TodoManagement: 删除任务', id);
                        await _todo.TodoService.deleteTodo(id);
                        setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                            console.error('刷新统计数据失败:', statsError);
                        // 统计数据刷新失败不影响主要操作
                        }
                        _antd.message.success('任务删除成功');
                    } catch (error) {
                        console.error('删除任务失败:', error);
                        _antd.message.error('删除任务失败，请稍后重试');
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: "dashboard-card",
                    style: {
                        borderRadius: 12,
                        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                        border: 'none',
                        background: 'linear-gradient(145deg, #ffffff, #f5f8ff)'
                    },
                    title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                        justify: "space-between",
                        align: "center",
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            strong: true,
                            children: "待办事项"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 238,
                            columnNumber: 11
                        }, void 0)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 237,
                        columnNumber: 9
                    }, void 0),
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16,
                                padding: '12px 16px',
                                background: '#fafbfc',
                                borderRadius: 8,
                                border: '1px solid #f0f0f0'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                gutter: [
                                    16,
                                    12
                                ],
                                align: "middle",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 24,
                                        md: 8,
                                        lg: 8,
                                        xl: 8,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 12,
                                            style: {
                                                width: '100%'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                                    placeholder: "搜索任务...",
                                                    allowClear: true,
                                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 260,
                                                        columnNumber: 25
                                                    }, void 0),
                                                    value: searchText,
                                                    onChange: (e)=>setSearchText(e.target.value),
                                                    style: {
                                                        flex: 1
                                                    },
                                                    size: "middle"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 257,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 269,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    onClick: ()=>{
                                                        setEditingTodoId(null);
                                                        todoForm.resetFields();
                                                        setTodoModalVisible(true);
                                                    },
                                                    style: {
                                                        background: '#1890ff',
                                                        borderColor: '#1890ff',
                                                        boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
                                                        fontWeight: 500,
                                                        minWidth: 80
                                                    },
                                                    size: "middle",
                                                    children: "新增"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 256,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 255,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 24,
                                        md: 8,
                                        lg: 8,
                                        xl: 8,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            justify: "center",
                                            wrap: "wrap",
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                size: 12,
                                                wrap: true,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                        title: `高优先级任务: ${todoStats.highPriorityCount}个`,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            align: "center",
                                                            gap: 4,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        width: 8,
                                                                        height: 8,
                                                                        borderRadius: '50%',
                                                                        background: '#ff4d4f'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 297,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        fontWeight: 500,
                                                                        color: '#262626'
                                                                    },
                                                                    children: [
                                                                        "高: ",
                                                                        todoStats.highPriorityCount
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 305,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 296,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 293,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                        title: `中优先级任务: ${todoStats.mediumPriorityCount}个`,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            align: "center",
                                                            gap: 4,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        width: 8,
                                                                        height: 8,
                                                                        borderRadius: '50%',
                                                                        background: '#faad14'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 321,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        fontWeight: 500,
                                                                        color: '#262626'
                                                                    },
                                                                    children: [
                                                                        "中: ",
                                                                        todoStats.mediumPriorityCount
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 329,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 320,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 317,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                        title: `低优先级任务: ${todoStats.lowPriorityCount}个`,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            align: "center",
                                                            gap: 4,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        width: 8,
                                                                        height: 8,
                                                                        borderRadius: '50%',
                                                                        background: '#52c41a'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 345,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        fontWeight: 500,
                                                                        color: '#262626'
                                                                    },
                                                                    children: [
                                                                        "低: ",
                                                                        todoStats.lowPriorityCount
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 353,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 344,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 341,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 292,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 291,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 290,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 24,
                                        md: 8,
                                        lg: 8,
                                        xl: 8,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            justify: "center",
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                title: `完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    align: "center",
                                                    gap: 6,
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            style: {
                                                                fontSize: 12,
                                                                fontWeight: 500,
                                                                color: '#595959'
                                                            },
                                                            children: "完成率:"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 375,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                            percent: todoStats.completionPercentage,
                                                            size: "small",
                                                            style: {
                                                                width: 80
                                                            },
                                                            strokeColor: "#52c41a",
                                                            showInfo: false
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 380,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            style: {
                                                                fontSize: 12,
                                                                fontWeight: 600,
                                                                color: '#262626'
                                                            },
                                                            children: [
                                                                todoStats.completionPercentage,
                                                                "%"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 387,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 374,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 371,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 370,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 369,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 253,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 243,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: (key)=>setActiveTab(key),
                            size: "middle",
                            style: {
                                marginBottom: 8
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "全部"
                                }, "all", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 406,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "待处理"
                                }, "pending", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 407,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "已完成"
                                }, "completed", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 408,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 400,
                            columnNumber: 7
                        }, this),
                        error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "TODO数据加载失败",
                            description: error,
                            type: "error",
                            showIcon: true,
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 413,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: loading,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                    dataSource: filteredPersonalTasks,
                                    renderItem: (item)=>{
                                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                            className: "todo-item",
                                            style: {
                                                padding: '10px 16px',
                                                marginBottom: 12,
                                                borderRadius: 8,
                                                background: '#fff',
                                                opacity: item.status === 1 ? 0.7 : 1,
                                                borderLeft: `3px solid ${item.status === 1 ? '#52c41a' : item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`,
                                                boxShadow: '0 1px 4px rgba(0,0,0,0.05)'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 12,
                                                style: {
                                                    width: '100%'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        children: [
                                                            item.status === 1 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                justify: "center",
                                                                style: {
                                                                    width: 22,
                                                                    height: 22,
                                                                    borderRadius: '50%',
                                                                    background: '#52c41a'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                    style: {
                                                                        color: '#fff',
                                                                        fontSize: 12
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 460,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 450,
                                                                columnNumber: 25
                                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 18,
                                                                    height: 18,
                                                                    borderRadius: '50%',
                                                                    border: `2px solid ${item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 465,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 2,
                                                                    height: 24,
                                                                    background: '#f0f0f0',
                                                                    marginTop: 4
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 481,
                                                                columnNumber: 23
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 448,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        style: {
                                                            flex: 1
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 14,
                                                                    fontWeight: item.priority === 3 ? 500 : 'normal',
                                                                    textDecoration: item.status === 1 ? 'line-through' : 'none',
                                                                    color: item.status === 1 ? '#8c8c8c' : '#262626'
                                                                },
                                                                children: item.title
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 493,
                                                                columnNumber: 23
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                align: "center",
                                                                size: 6,
                                                                style: {
                                                                    marginTop: 4
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#8c8c8c'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 507,
                                                                        columnNumber: 25
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            "创建于:",
                                                                            ' ',
                                                                            new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 513,
                                                                        columnNumber: 25
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 506,
                                                                columnNumber: 23
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 492,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                        trigger: [
                                                            'click'
                                                        ],
                                                        menu: {
                                                            items: [
                                                                {
                                                                    key: 'complete',
                                                                    label: item.status === 1 ? '标记未完成' : '标记完成',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                        style: {
                                                                            color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 530,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'edit',
                                                                    label: '编辑任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                                        style: {
                                                                            color: '#8c8c8c'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 542,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'delete',
                                                                    label: '删除任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                                        style: {
                                                                            color: '#ff4d4f'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 548,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    danger: true
                                                                }
                                                            ],
                                                            onClick: ({ key })=>{
                                                                if (key === 'complete') handleToggleTodoStatus(item.id);
                                                                else if (key === 'edit') {
                                                                    setEditingTodoId(item.id);
                                                                    todoForm.setFieldsValue({
                                                                        name: item.title,
                                                                        priority: item.priority
                                                                    });
                                                                    setTodoModalVisible(true);
                                                                } else if (key === 'delete') handleDeleteTodo(item.id);
                                                            }
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            type: "text",
                                                            size: "small",
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 572,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            style: {
                                                                width: 32,
                                                                height: 32
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 569,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 521,
                                                        columnNumber: 21
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 446,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 426,
                                            columnNumber: 17
                                        }, void 0);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 422,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                                    title: editingTodoId ? '编辑待办事项' : '新增待办事项',
                                    open: todoModalVisible,
                                    onCancel: ()=>{
                                        setTodoModalVisible(false);
                                        todoForm.resetFields();
                                    },
                                    onOk: ()=>{
                                        todoForm.submit();
                                    },
                                    centered: true,
                                    destroyOnClose: true,
                                    footer: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            onClick: ()=>setTodoModalVisible(false),
                                            children: "取消"
                                        }, "cancel", false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 596,
                                            columnNumber: 15
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            onClick: ()=>{
                                                todoForm.submit();
                                            },
                                            style: {
                                                background: '#1890ff',
                                                borderColor: '#1890ff',
                                                boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)'
                                            },
                                            children: editingTodoId ? '更新任务' : '创建任务'
                                        }, "submit", false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 599,
                                            columnNumber: 15
                                        }, void 0)
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                        form: todoForm,
                                        layout: "vertical",
                                        onFinish: handleAddOrUpdateTodo,
                                        autoComplete: "off",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "name",
                                                label: "任务名称",
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: '请输入任务名称'
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                    placeholder: "请输入任务名称",
                                                    size: "large",
                                                    style: {
                                                        borderRadius: 6
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 626,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 621,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                                name: "priority",
                                                label: "优先级",
                                                initialValue: 2,
                                                rules: [
                                                    {
                                                        required: true,
                                                        message: '请选择优先级'
                                                    }
                                                ],
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                    size: "large",
                                                    options: [
                                                        {
                                                            value: 3,
                                                            label: '高优先级'
                                                        },
                                                        {
                                                            value: 2,
                                                            label: '中优先级'
                                                        },
                                                        {
                                                            value: 1,
                                                            label: '低优先级'
                                                        }
                                                    ],
                                                    style: {
                                                        borderRadius: 6
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 639,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 633,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 615,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 583,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 421,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                    lineNumber: 228,
                    columnNumber: 5
                }, this);
            };
            _s(TodoManagement, "BRDE78r/O6qdRZ096tTY2qeEVcA=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TodoManagement;
            var _default = TodoManagement;
            var _c;
            $RefreshReg$(_c, "TodoManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '4035608122203272327';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.8208025718815386209.hot-update.js.map