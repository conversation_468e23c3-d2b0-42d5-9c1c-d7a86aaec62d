globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/pages/subscription/components/SubscriptionPlansContent.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            const SubscriptionPlansContent = ({ onSubscriptionSuccess })=>{
                _s();
                const [loading, setLoading] = (0, _react.useState)(true);
                const [plans, setPlans] = (0, _react.useState)([]);
                const [subscribing, setSubscribing] = (0, _react.useState)(false);
                const [selectedPlan, setSelectedPlan] = (0, _react.useState)(null);
                const [subscribeModalVisible, setSubscribeModalVisible] = (0, _react.useState)(false);
                const [duration, setDuration] = (0, _react.useState)(1);
                (0, _react.useEffect)(()=>{
                    fetchPlans();
                }, []);
                const fetchPlans = async ()=>{
                    try {
                        setLoading(true);
                        const planList = await _services.SubscriptionService.getActivePlans();
                        setPlans(planList);
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    } finally{
                        setLoading(false);
                    }
                };
                const handleSubscribe = (plan)=>{
                    setSelectedPlan(plan);
                    setDuration(1);
                    setSubscribeModalVisible(true);
                };
                const handleConfirmSubscribe = async ()=>{
                    if (!selectedPlan) return;
                    try {
                        setSubscribing(true);
                        const request = {
                            planId: selectedPlan.id,
                            duration
                        };
                        await _services.SubscriptionService.createSubscription(request);
                        setSubscribeModalVisible(false);
                        _antd.message.success('订阅成功！');
                        onSubscriptionSuccess === null || onSubscriptionSuccess === void 0 || onSubscriptionSuccess();
                    } catch (error) {
                        console.error('订阅失败:', error);
                    } finally{
                        setSubscribing(false);
                    }
                };
                const getPlanFeatures = (plan)=>{
                    const features = [
                        `数据存储上限：${plan.maxSize}GB`,
                        '7x24小时技术支持',
                        '数据备份与恢复',
                        '团队协作功能'
                    ];
                    if (plan.price > 0) {
                        features.push('高级分析报告');
                        features.push('API 访问权限');
                    }
                    if (plan.price >= 100) {
                        features.push('专属客户经理');
                        features.push('定制化功能');
                        features.push('优先技术支持');
                    }
                    return features;
                };
                const getPlanColor = (plan)=>{
                    if (plan.price === 0) return '#52c41a'; // 免费版 - 绿色
                    if (plan.price < 100) return '#1890ff'; // 基础版 - 蓝色
                    return '#722ed1'; // 专业版 - 紫色
                };
                const getPlanIcon = (plan)=>{
                    if (plan.price === 0) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {}, void 0, false, {
                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                        lineNumber: 120,
                        columnNumber: 34
                    }, this);
                    if (plan.price < 100) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StarOutlined, {}, void 0, false, {
                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                        lineNumber: 121,
                        columnNumber: 34
                    }, this);
                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                        lineNumber: 122,
                        columnNumber: 12
                    }, this);
                };
                const calculatePrice = (plan, months)=>{
                    return _services.SubscriptionService.calculatePlanPrice(plan, months);
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                24,
                                24
                            ],
                            children: plans.map((plan)=>{
                                const features = getPlanFeatures(plan);
                                const color = getPlanColor(plan);
                                const icon = getPlanIcon(plan);
                                const isPopular = plan.price > 0 && plan.price < 100;
                                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 12,
                                    lg: 8,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                        hoverable: true,
                                        loading: loading,
                                        style: {
                                            height: '100%',
                                            borderColor: isPopular ? '#1890ff' : undefined,
                                            position: 'relative'
                                        },
                                        children: [
                                            isPopular && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    position: 'absolute',
                                                    top: -1,
                                                    right: 24,
                                                    background: '#1890ff',
                                                    color: 'white',
                                                    padding: '4px 12px',
                                                    borderRadius: '0 0 8px 8px',
                                                    fontSize: 12,
                                                    fontWeight: 'bold'
                                                },
                                                children: "推荐"
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 150,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    textAlign: 'center',
                                                    marginBottom: 24
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 48,
                                                            color,
                                                            marginBottom: 16
                                                        },
                                                        children: icon
                                                    }, void 0, false, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 168,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                        level: 3,
                                                        style: {
                                                            margin: 0,
                                                            color
                                                        },
                                                        children: plan.name
                                                    }, void 0, false, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 171,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        type: "secondary",
                                                        children: plan.description
                                                    }, void 0, false, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 174,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 167,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    textAlign: 'center',
                                                    marginBottom: 24
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            fontSize: 36,
                                                            fontWeight: 'bold',
                                                            color
                                                        },
                                                        children: [
                                                            "¥",
                                                            plan.price,
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                style: {
                                                                    fontSize: 16,
                                                                    fontWeight: 'normal'
                                                                },
                                                                children: "/月"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                                lineNumber: 180,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 178,
                                                        columnNumber: 19
                                                    }, this),
                                                    plan.price === 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                                                        color: "green",
                                                        style: {
                                                            marginTop: 8
                                                        },
                                                        children: "永久免费"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 185,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 177,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                                size: "small",
                                                dataSource: features,
                                                renderItem: (feature)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                style: {
                                                                    color: '#52c41a',
                                                                    marginRight: 8
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                                lineNumber: 196,
                                                                columnNumber: 23
                                                            }, void 0),
                                                            feature
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 195,
                                                        columnNumber: 21
                                                    }, void 0),
                                                style: {
                                                    marginBottom: 24
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 191,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: isPopular ? 'primary' : 'default',
                                                size: "large",
                                                block: true,
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ShoppingCartOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                    lineNumber: 209,
                                                    columnNumber: 25
                                                }, void 0),
                                                onClick: ()=>handleSubscribe(plan),
                                                disabled: plan.price === 0,
                                                children: plan.price === 0 ? '当前套餐' : '立即订阅'
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 205,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 140,
                                        columnNumber: 15
                                    }, this)
                                }, plan.id, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 139,
                                    columnNumber: 13
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 131,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: "套餐对比",
                            style: {
                                marginTop: 32
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    overflowX: 'auto'
                                },
                                children: plans.length > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    children: _services.SubscriptionService.comparePlans(plans).map((comparison, index)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                            style: {
                                                padding: '12px 0',
                                                borderBottom: '1px solid #f0f0f0'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    span: 6,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        strong: true,
                                                        children: comparison.feature
                                                    }, void 0, false, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 236,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                    lineNumber: 235,
                                                    columnNumber: 21
                                                }, this),
                                                comparison.values.map((value, valueIndex)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        span: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            children: typeof value === 'boolean' ? value ? '✓' : '✗' : value
                                                        }, void 0, false, {
                                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                            lineNumber: 240,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, `value-${index}-${valueIndex}`, false, {
                                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                        lineNumber: 239,
                                                        columnNumber: 23
                                                    }, this))
                                            ]
                                        }, `comparison-${index}`, true, {
                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                            lineNumber: 228,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 225,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                lineNumber: 223,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 222,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                            title: "确认订阅",
                            open: subscribeModalVisible,
                            onCancel: ()=>setSubscribeModalVisible(false),
                            footer: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    onClick: ()=>setSubscribeModalVisible(false),
                                    children: "取消"
                                }, "cancel", false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 263,
                                    columnNumber: 11
                                }, void 0),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    loading: subscribing,
                                    onClick: handleConfirmSubscribe,
                                    children: "确认订阅"
                                }, "confirm", false, {
                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                    lineNumber: 266,
                                    columnNumber: 11
                                }, void 0)
                            ],
                            children: selectedPlan && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginBottom: 16
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                strong: true,
                                                children: "套餐："
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 279,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                children: selectedPlan.name
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 280,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 278,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            marginBottom: 16
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                strong: true,
                                                children: "订阅时长："
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 284,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                                min: 1,
                                                max: 24,
                                                value: duration,
                                                onChange: (value)=>setDuration(value || 1),
                                                addonAfter: "个月",
                                                style: {
                                                    marginLeft: 8
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 285,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 283,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 295,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                strong: true,
                                                children: "价格详情："
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                lineNumber: 298,
                                                columnNumber: 15
                                            }, this),
                                            (()=>{
                                                const priceInfo = calculatePrice(selectedPlan, duration);
                                                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        marginTop: 8
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            children: [
                                                                "原价：¥",
                                                                priceInfo.originalPrice
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                            lineNumber: 303,
                                                            columnNumber: 21
                                                        }, this),
                                                        priceInfo.discount > 0 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                color: '#ff4d4f'
                                                            },
                                                            children: [
                                                                "折扣：-",
                                                                priceInfo.discount,
                                                                "%"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                            lineNumber: 305,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                fontSize: 18,
                                                                fontWeight: 'bold',
                                                                color: '#1890ff'
                                                            },
                                                            children: [
                                                                "总计：¥",
                                                                priceInfo.totalPrice
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                            lineNumber: 309,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                                    lineNumber: 302,
                                                    columnNumber: 19
                                                }, this);
                                            })()
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                        lineNumber: 297,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                                lineNumber: 277,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                            lineNumber: 258,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/SubscriptionPlansContent.tsx",
                    lineNumber: 130,
                    columnNumber: 5
                }, this);
            };
            _s(SubscriptionPlansContent, "r8YRf6YYs6oegStol4umzcvp5Ls=");
            _c = SubscriptionPlansContent;
            var _default = SubscriptionPlansContent;
            var _c;
            $RefreshReg$(_c, "SubscriptionPlansContent");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '1559473121463508828';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.364645812957624839.hot-update.js.map