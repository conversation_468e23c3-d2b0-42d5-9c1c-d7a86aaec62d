{"version": 3, "sources": ["umi.6605424396301513257.hot-update.js", "src/components/FloatButton/index.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='16846954939273623498';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  CalendarOutlined,\n  ClockCircleOutlined,\n  CloseOutlined,\n  CrownOutlined,\n  LogoutOutlined,\n  MailOutlined,\n  MenuOutlined,\n  PhoneOutlined,\n  QuestionCircleOutlined,\n  SaveOutlined,\n  SettingOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  FloatButton,\n  Form,\n  Input,\n  Modal,\n  message,\n  Space,\n  Tabs,\n  Tag,\n  Typography,\n} from 'antd';\nimport React, { useState } from 'react';\nimport SubscriptionPlansContent from '@/pages/subscription/components/SubscriptionPlansContent';\nimport { AuthService, SubscriptionService, UserService } from '@/services';\nimport type {\n  SubscriptionResponse,\n  UpdateUserProfileRequest,\n} from '@/types/api';\n\n// const { Title } = Typography;\n\ninterface UserFloatButtonProps {\n  style?: React.CSSProperties;\n}\n\nconst UserFloatButton: React.FC<UserFloatButtonProps> = ({ style }) => {\n  const [open, setOpen] = useState(false);\n  const [logoutModalVisible, setLogoutModalVisible] = useState(false);\n  const [logoutLoading, setLogoutLoading] = useState(false);\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('profile');\n  const [profileForm] = Form.useForm();\n  const [profileLoading, setProfileLoading] = useState(false);\n  const [currentSubscription, setCurrentSubscription] =\n    useState<SubscriptionResponse | null>(null);\n  const [subscriptionLoading, setSubscriptionLoading] = useState(false);\n\n  const { initialState, setInitialState } = useModel('@@initialState');\n\n  // 如果用户未登录，不显示浮动按钮\n  // 确保用户已登录后就显示 FloatButton，无需等待团队选择\n  if (!initialState?.currentUser) {\n    return null;\n  }\n\n  // 处理个人中心跳转\n  const handlePersonalCenter = () => {\n    setOpen(false);\n    history.push('/personal-center');\n  };\n\n  // 处理设置\n  const handleSettings = async () => {\n    setOpen(false);\n    if (initialState?.currentUser) {\n      profileForm.setFieldsValue({\n        name: initialState.currentUser.name,\n        email: initialState.currentUser.email,\n        telephone: initialState.currentUser.telephone || '',\n      });\n    }\n\n    // 获取当前订阅信息\n    setSubscriptionLoading(true);\n    try {\n      const subscription = await SubscriptionService.getCurrentSubscription();\n      setCurrentSubscription(subscription);\n    } catch (error) {\n      setCurrentSubscription(null);\n    } finally {\n      setSubscriptionLoading(false);\n    }\n\n    setSettingsModalVisible(true);\n  };\n\n  // 处理帮助文档\n  const handleHelp = () => {\n    setOpen(false);\n    history.push('/help');\n  };\n\n  // 保存用户资料\n  const handleSaveProfile = async (values: any) => {\n    try {\n      setProfileLoading(true);\n      const updateData: UpdateUserProfileRequest = {\n        name: values.name,\n        telephone: values.telephone,\n      };\n\n      const updatedProfile = await UserService.updateUserProfile(updateData);\n\n      // 更新 initialState 中的用户信息\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: {\n          ...prevState?.currentUser,\n          ...updatedProfile,\n        },\n      }));\n\n      message.success('个人资料更新成功');\n    } catch (error) {\n      console.error('更新个人资料失败:', error);\n      message.error('更新个人资料失败');\n    } finally {\n      setProfileLoading(false);\n    }\n  };\n\n  // 处理订阅成功\n  const handleSubscriptionSuccess = async () => {\n    // 刷新当前订阅信息\n    try {\n      const subscription = await SubscriptionService.getCurrentSubscription();\n      setCurrentSubscription(subscription);\n    } catch (error) {\n      console.error('刷新订阅信息失败:', error);\n    }\n\n    setSettingsModalVisible(false);\n    message.success('订阅成功！');\n  };\n\n  // 计算剩余天数\n  const calculateRemainingDays = (endDate: string): number => {\n    const end = new Date(endDate);\n    const now = new Date();\n    const diffTime = end.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  // 处理退出登录\n  const handleLogout = async () => {\n    try {\n      setLogoutLoading(true);\n\n      // 调用退出登录API\n      await AuthService.logout();\n\n      // 清除 initialState\n      if (setInitialState) {\n        await setInitialState({\n          currentUser: undefined,\n          currentTeam: undefined,\n        });\n      }\n\n      // 跳转到登录页面\n      history.push('/user/login');\n    } catch (error) {\n      // 即使API调用失败，也要清除本地状态并跳转\n      if (setInitialState) {\n        await setInitialState({\n          currentUser: undefined,\n          currentTeam: undefined,\n        });\n      }\n      history.push('/user/login');\n    } finally {\n      setLogoutLoading(false);\n      setLogoutModalVisible(false);\n      setOpen(false);\n    }\n  };\n\n  const floatButtonItems = [\n    {\n      key: 'personal-center',\n      icon: <UserOutlined />,\n      tooltip: '个人中心',\n      onClick: handlePersonalCenter,\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      tooltip: '设置',\n      onClick: handleSettings,\n    },\n    {\n      key: 'help',\n      icon: <QuestionCircleOutlined />,\n      tooltip: '帮助文档',\n      onClick: handleHelp,\n    },\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      tooltip: '退出登录',\n      onClick: () => {\n        setOpen(false);\n        setLogoutModalVisible(true);\n      },\n    },\n  ];\n\n  return (\n    <>\n      <FloatButton.Group\n        open={open}\n        trigger=\"click\"\n        type=\"primary\"\n        placement=\"left\"\n        style={{\n          right: 24,\n          bottom: 24,\n          ...style,\n        }}\n        icon={open ? <CloseOutlined /> : <MenuOutlined />}\n        onClick={() => setOpen(!open)}\n      >\n        {floatButtonItems.map((item) => (\n          <FloatButton\n            key={item.key}\n            icon={item.icon}\n            tooltip={item.tooltip}\n            onClick={item.onClick}\n          />\n        ))}\n      </FloatButton.Group>\n\n      {/* 统一设置模态框 */}\n      <Modal\n        title={\n          <Space>\n            <SettingOutlined />\n            设置\n          </Space>\n        }\n        open={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        footer={null}\n        width={1200}\n        style={{ top: 20 }}\n      >\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={[\n            {\n              key: 'profile',\n              label: '个人资料',\n              children: (\n                <div style={{ padding: '20px 0' }}>\n                  {/* 表单部分 */}\n                  <Form\n                    form={profileForm}\n                    layout=\"vertical\"\n                    onFinish={handleSaveProfile}\n                  >\n                    <Form.Item\n                      label=\"用户名\"\n                      name=\"name\"\n                      rules={[\n                        { required: true, message: '请输入用户名' },\n                        { max: 100, message: '用户名不能超过100个字符' },\n                      ]}\n                    >\n                      <Input\n                        prefix={<UserOutlined />}\n                        placeholder=\"请输入用户名\"\n                      />\n                    </Form.Item>\n\n                    <Form.Item label=\"邮箱地址\" name=\"email\">\n                      <Input\n                        prefix={<MailOutlined />}\n                        disabled\n                        placeholder=\"邮箱地址不可修改\"\n                      />\n                    </Form.Item>\n\n                    <Form.Item\n                      label=\"手机号\"\n                      name=\"telephone\"\n                      rules={[\n                        {\n                          pattern: /^1[3-9]\\d{9}$/,\n                          message: '请输入正确的手机号格式',\n                        },\n                      ]}\n                    >\n                      <Input\n                        prefix={<PhoneOutlined />}\n                        placeholder=\"请输入手机号\"\n                        maxLength={11}\n                      />\n                    </Form.Item>\n\n                    <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>\n                      <Space>\n                        <Button onClick={() => setSettingsModalVisible(false)}>\n                          取消\n                        </Button>\n                        <Button\n                          type=\"primary\"\n                          htmlType=\"submit\"\n                          loading={profileLoading}\n                          icon={<SaveOutlined />}\n                        >\n                          保存修改\n                        </Button>\n                      </Space>\n                    </Form.Item>\n                  </Form>\n                </div>\n              ),\n            },\n            {\n              key: 'subscription',\n              label: '订阅套餐',\n              children: (\n                <div style={{ padding: '20px 0' }}>\n                  {/* 当前套餐信息 */}\n                  <div style={{ marginBottom: 24 }}>\n                    <Typography.Title level={4} style={{ marginBottom: 16 }}>\n                      <CrownOutlined\n                        style={{ marginRight: 8, color: '#faad14' }}\n                      />\n                      当前套餐\n                    </Typography.Title>\n\n                    {subscriptionLoading ? (\n                      <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                        加载中...\n                      </div>\n                    ) : currentSubscription ? (\n                      <Card\n                        size=\"small\"\n                        style={{\n                          background: '#f6ffed',\n                          border: '1px solid #b7eb8f',\n                          marginBottom: 16,\n                        }}\n                      >\n                        <Space direction=\"vertical\" style={{ width: '100%' }}>\n                          <div\n                            style={{\n                              display: 'flex',\n                              justifyContent: 'space-between',\n                              alignItems: 'center',\n                            }}\n                          >\n                            <Typography.Text strong style={{ fontSize: 16 }}>\n                              {currentSubscription.planName}\n                            </Typography.Text>\n                            <Tag\n                              color={\n                                currentSubscription.status === 'ACTIVE'\n                                  ? 'green'\n                                  : 'orange'\n                              }\n                            >\n                              {currentSubscription.status === 'ACTIVE'\n                                ? '有效'\n                                : '已过期'}\n                            </Tag>\n                          </div>\n\n                          <Typography.Text type=\"secondary\">\n                            {currentSubscription.planDescription}\n                          </Typography.Text>\n\n                          <div\n                            style={{\n                              display: 'flex',\n                              justifyContent: 'space-between',\n                            }}\n                          >\n                            <Space>\n                              <CalendarOutlined />\n                              <span>\n                                到期时间:{' '}\n                                {new Date(\n                                  currentSubscription.endDate,\n                                ).toLocaleDateString()}\n                              </span>\n                            </Space>\n                            <Space>\n                              <ClockCircleOutlined />\n                              <span>\n                                剩余:{' '}\n                                {calculateRemainingDays(\n                                  currentSubscription.endDate,\n                                )}{' '}\n                                天\n                              </span>\n                            </Space>\n                          </div>\n                        </Space>\n                      </Card>\n                    ) : (\n                      <Card\n                        size=\"small\"\n                        style={{\n                          background: '#fff2e8',\n                          border: '1px solid #ffbb96',\n                          marginBottom: 16,\n                        }}\n                      >\n                        <Typography.Text type=\"secondary\">\n                          您当前没有有效的订阅套餐，请选择合适的套餐进行订阅\n                        </Typography.Text>\n                      </Card>\n                    )}\n                  </div>\n\n                  {/* 可选套餐列表 */}\n                  <div>\n                    <Typography.Title level={4} style={{ marginBottom: 16 }}>\n                      选择套餐\n                    </Typography.Title>\n                    <SubscriptionPlansContent\n                      onSubscriptionSuccess={handleSubscriptionSuccess}\n                    />\n                  </div>\n                </div>\n              ),\n            },\n          ]}\n        />\n      </Modal>\n\n      {/* 退出登录确认模态框 */}\n      <Modal\n        title=\"确认退出登录\"\n        open={logoutModalVisible}\n        onCancel={() => setLogoutModalVisible(false)}\n        onOk={handleLogout}\n        confirmLoading={logoutLoading}\n        okText=\"确认退出\"\n        cancelText=\"取消\"\n        okButtonProps={{ danger: true }}\n        width={400}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          <LogoutOutlined\n            style={{ fontSize: 48, color: '#ff4d4f', marginBottom: 16 }}\n          />\n          <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>\n            您确定要退出登录吗？\n          </div>\n          <div style={{ color: '#666', fontSize: 14 }}>\n            退出后您需要重新登录才能继续使用系统\n          </div>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default UserFloatButton;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;wCCmdb;;;2BAAA;;;;;;;0CAzcO;wCAC2B;yCAa3B;oFACyB;sGACK;6CACyB;;;;;;;;;;YAY9D,MAAM,kBAAkD,CAAC,EAAE,KAAK,EAAE;;gBAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,eAAQ,EAAC;gBACjC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;gBAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAC;gBACnD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;gBACjE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;gBAC3C,MAAM,CAAC,YAAY,GAAG,UAAI,CAAC,OAAO;gBAClC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAAC;gBACrD,MAAM,CAAC,qBAAqB,uBAAuB,GACjD,IAAA,eAAQ,EAA8B;gBACxC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAC;gBAE/D,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;gBAEnD,kBAAkB;gBAClB,mCAAmC;gBACnC,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAC5B,OAAO;gBAGT,WAAW;gBACX,MAAM,uBAAuB;oBAC3B,QAAQ;oBACR,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,OAAO;gBACP,MAAM,iBAAiB;oBACrB,QAAQ;oBACR,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B,YAAY,cAAc,CAAC;wBACzB,MAAM,aAAa,WAAW,CAAC,IAAI;wBACnC,OAAO,aAAa,WAAW,CAAC,KAAK;wBACrC,WAAW,aAAa,WAAW,CAAC,SAAS,IAAI;oBACnD;oBAGF,WAAW;oBACX,uBAAuB;oBACvB,IAAI;wBACF,MAAM,eAAe,MAAM,6BAAmB,CAAC,sBAAsB;wBACrE,uBAAuB;oBACzB,EAAE,OAAO,OAAO;wBACd,uBAAuB;oBACzB,SAAU;wBACR,uBAAuB;oBACzB;oBAEA,wBAAwB;gBAC1B;gBAEA,SAAS;gBACT,MAAM,aAAa;oBACjB,QAAQ;oBACR,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,SAAS;gBACT,MAAM,oBAAoB,OAAO;oBAC/B,IAAI;wBACF,kBAAkB;wBAClB,MAAM,aAAuC;4BAC3C,MAAM,OAAO,IAAI;4BACjB,WAAW,OAAO,SAAS;wBAC7B;wBAEA,MAAM,iBAAiB,MAAM,qBAAW,CAAC,iBAAiB,CAAC;wBAE3D,yBAAyB;wBACzB,MAAM,gBAAgB,CAAC,YAAe,CAAA;gCACpC,GAAG,SAAS;gCACZ,aAAa;uCACR,sBAAA,gCAAA,UAAW,WAAW,AAAzB;oCACA,GAAG,cAAc;gCACnB;4BACF,CAAA;wBAEA,aAAO,CAAC,OAAO,CAAC;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,aAAO,CAAC,KAAK,CAAC;oBAChB,SAAU;wBACR,kBAAkB;oBACpB;gBACF;gBAEA,SAAS;gBACT,MAAM,4BAA4B;oBAChC,WAAW;oBACX,IAAI;wBACF,MAAM,eAAe,MAAM,6BAAmB,CAAC,sBAAsB;wBACrE,uBAAuB;oBACzB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;oBAC7B;oBAEA,wBAAwB;oBACxB,aAAO,CAAC,OAAO,CAAC;gBAClB;gBAEA,SAAS;gBACT,MAAM,yBAAyB,CAAC;oBAC9B,MAAM,MAAM,IAAI,KAAK;oBACrB,MAAM,MAAM,IAAI;oBAChB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,OAAO;oBAC5C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAY;oBACvC,OAAO,KAAK,GAAG,CAAC,GAAG;gBACrB;gBAEA,SAAS;gBACT,MAAM,eAAe;oBACnB,IAAI;wBACF,iBAAiB;wBAEjB,YAAY;wBACZ,MAAM,qBAAW,CAAC,MAAM;wBAExB,kBAAkB;wBAClB,IAAI,iBACF,MAAM,gBAAgB;4BACpB,aAAa;4BACb,aAAa;wBACf;wBAGF,UAAU;wBACV,YAAO,CAAC,IAAI,CAAC;oBACf,EAAE,OAAO,OAAO;wBACd,wBAAwB;wBACxB,IAAI,iBACF,MAAM,gBAAgB;4BACpB,aAAa;4BACb,aAAa;wBACf;wBAEF,YAAO,CAAC,IAAI,CAAC;oBACf,SAAU;wBACR,iBAAiB;wBACjB,sBAAsB;wBACtB,QAAQ;oBACV;gBACF;gBAEA,MAAM,mBAAmB;oBACvB;wBACE,KAAK;wBACL,oBAAM,2BAAC,mBAAY;;;;;wBACnB,SAAS;wBACT,SAAS;oBACX;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,sBAAe;;;;;wBACtB,SAAS;wBACT,SAAS;oBACX;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,6BAAsB;;;;;wBAC7B,SAAS;wBACT,SAAS;oBACX;oBACA;wBACE,KAAK;wBACL,oBAAM,2BAAC,qBAAc;;;;;wBACrB,SAAS;wBACT,SAAS;4BACP,QAAQ;4BACR,sBAAsB;wBACxB;oBACF;iBACD;gBAED,qBACE;;sCACE,2BAAC,iBAAW,CAAC,KAAK;4BAChB,MAAM;4BACN,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,OAAO;gCACL,OAAO;gCACP,QAAQ;gCACR,GAAG,KAAK;4BACV;4BACA,MAAM,qBAAO,2BAAC,oBAAa;;;;uDAAM,2BAAC,mBAAY;;;;;4BAC9C,SAAS,IAAM,QAAQ,CAAC;sCAEvB,iBAAiB,GAAG,CAAC,CAAC,qBACrB,2BAAC,iBAAW;oCAEV,MAAM,KAAK,IAAI;oCACf,SAAS,KAAK,OAAO;oCACrB,SAAS,KAAK,OAAO;mCAHhB,KAAK,GAAG;;;;;;;;;;sCASnB,2BAAC,WAAK;4BACJ,qBACE,2BAAC,WAAK;;kDACJ,2BAAC,sBAAe;;;;;oCAAG;;;;;;;4BAIvB,MAAM;4BACN,UAAU,IAAM,wBAAwB;4BACxC,QAAQ;4BACR,OAAO;4BACP,OAAO;gCAAE,KAAK;4BAAG;sCAEjB,cAAA,2BAAC,UAAI;gCACH,WAAW;gCACX,UAAU;gCACV,OAAO;oCACL;wCACE,KAAK;wCACL,OAAO;wCACP,wBACE,2BAAC;4CAAI,OAAO;gDAAE,SAAS;4CAAS;sDAE9B,cAAA,2BAAC,UAAI;gDACH,MAAM;gDACN,QAAO;gDACP,UAAU;;kEAEV,2BAAC,UAAI,CAAC,IAAI;wDACR,OAAM;wDACN,MAAK;wDACL,OAAO;4DACL;gEAAE,UAAU;gEAAM,SAAS;4DAAS;4DACpC;gEAAE,KAAK;gEAAK,SAAS;4DAAgB;yDACtC;kEAED,cAAA,2BAAC,WAAK;4DACJ,sBAAQ,2BAAC,mBAAY;;;;;4DACrB,aAAY;;;;;;;;;;;kEAIhB,2BAAC,UAAI,CAAC,IAAI;wDAAC,OAAM;wDAAO,MAAK;kEAC3B,cAAA,2BAAC,WAAK;4DACJ,sBAAQ,2BAAC,mBAAY;;;;;4DACrB,QAAQ;4DACR,aAAY;;;;;;;;;;;kEAIhB,2BAAC,UAAI,CAAC,IAAI;wDACR,OAAM;wDACN,MAAK;wDACL,OAAO;4DACL;gEACE,SAAS;gEACT,SAAS;4DACX;yDACD;kEAED,cAAA,2BAAC,WAAK;4DACJ,sBAAQ,2BAAC,oBAAa;;;;;4DACtB,aAAY;4DACZ,WAAW;;;;;;;;;;;kEAIf,2BAAC,UAAI,CAAC,IAAI;wDAAC,OAAO;4DAAE,cAAc;4DAAG,WAAW;wDAAQ;kEACtD,cAAA,2BAAC,WAAK;;8EACJ,2BAAC,YAAM;oEAAC,SAAS,IAAM,wBAAwB;8EAAQ;;;;;;8EAGvD,2BAAC,YAAM;oEACL,MAAK;oEACL,UAAS;oEACT,SAAS;oEACT,oBAAM,2BAAC,mBAAY;;;;;8EACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQb;oCACA;wCACE,KAAK;wCACL,OAAO;wCACP,wBACE,2BAAC;4CAAI,OAAO;gDAAE,SAAS;4CAAS;;8DAE9B,2BAAC;oDAAI,OAAO;wDAAE,cAAc;oDAAG;;sEAC7B,2BAAC,gBAAU,CAAC,KAAK;4DAAC,OAAO;4DAAG,OAAO;gEAAE,cAAc;4DAAG;;8EACpD,2BAAC,oBAAa;oEACZ,OAAO;wEAAE,aAAa;wEAAG,OAAO;oEAAU;;;;;;gEAC1C;;;;;;;wDAIH,oCACC,2BAAC;4DAAI,OAAO;gEAAE,WAAW;gEAAU,SAAS;4DAAS;sEAAG;;;;;qEAGtD,oCACF,2BAAC,UAAI;4DACH,MAAK;4DACL,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;4DAChB;sEAEA,cAAA,2BAAC,WAAK;gEAAC,WAAU;gEAAW,OAAO;oEAAE,OAAO;gEAAO;;kFACjD,2BAAC;wEACC,OAAO;4EACL,SAAS;4EACT,gBAAgB;4EAChB,YAAY;wEACd;;0FAEA,2BAAC,gBAAU,CAAC,IAAI;gFAAC,MAAM;gFAAC,OAAO;oFAAE,UAAU;gFAAG;0FAC3C,oBAAoB,QAAQ;;;;;;0FAE/B,2BAAC,SAAG;gFACF,OACE,oBAAoB,MAAM,KAAK,WAC3B,UACA;0FAGL,oBAAoB,MAAM,KAAK,WAC5B,OACA;;;;;;;;;;;;kFAIR,2BAAC,gBAAU,CAAC,IAAI;wEAAC,MAAK;kFACnB,oBAAoB,eAAe;;;;;;kFAGtC,2BAAC;wEACC,OAAO;4EACL,SAAS;4EACT,gBAAgB;wEAClB;;0FAEA,2BAAC,WAAK;;kGACJ,2BAAC,uBAAgB;;;;;kGACjB,2BAAC;;4FAAK;4FACE;4FACL,IAAI,KACH,oBAAoB,OAAO,EAC3B,kBAAkB;;;;;;;;;;;;;0FAGxB,2BAAC,WAAK;;kGACJ,2BAAC,0BAAmB;;;;;kGACpB,2BAAC;;4FAAK;4FACA;4FACH,uBACC,oBAAoB,OAAO;4FAC1B;4FAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mFAQjB,2BAAC,UAAI;4DACH,MAAK;4DACL,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,cAAc;4DAChB;sEAEA,cAAA,2BAAC,gBAAU,CAAC,IAAI;gEAAC,MAAK;0EAAY;;;;;;;;;;;;;;;;;8DAQxC,2BAAC;;sEACC,2BAAC,gBAAU,CAAC,KAAK;4DAAC,OAAO;4DAAG,OAAO;gEAAE,cAAc;4DAAG;sEAAG;;;;;;sEAGzD,2BAAC,iCAAwB;4DACvB,uBAAuB;;;;;;;;;;;;;;;;;;oCAKjC;iCACD;;;;;;;;;;;sCAKL,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,sBAAsB;4BACtC,MAAM;4BACN,gBAAgB;4BAChB,QAAO;4BACP,YAAW;4BACX,eAAe;gCAAE,QAAQ;4BAAK;4BAC9B,OAAO;sCAEP,cAAA,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAU,SAAS;gCAAS;;kDACnD,2BAAC,qBAAc;wCACb,OAAO;4CAAE,UAAU;4CAAI,OAAO;4CAAW,cAAc;wCAAG;;;;;;kDAE5D,2BAAC;wCAAI,OAAO;4CAAE,UAAU;4CAAI,YAAY;4CAAQ,cAAc;wCAAE;kDAAG;;;;;;kDAGnE,2BAAC;wCAAI,OAAO;4CAAE,OAAO;4CAAQ,UAAU;wCAAG;kDAAG;;;;;;;;;;;;;;;;;;;YAOvD;eA1aM;;oBAMkB,UAAI,CAAC;oBAMe,aAAQ;;;iBAZ9C;gBA4aN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDndD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC7kB"}