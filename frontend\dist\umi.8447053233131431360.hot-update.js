globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/app.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                getInitialState: function() {
                    return getInitialState;
                },
                layout: function() {
                    return layout;
                },
                request: function() {
                    return request;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _components = __mako_require__("src/components/index.ts");
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _services = __mako_require__("src/services/index.ts");
            var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            async function getInitialState() {
                /**
   * 获取用户信息的函数
   *
   * 从后端API获取当前登录用户的详细信息，包括用户ID、邮箱、姓名等。
   * 这个函数会被存储在全局状态中，供组件在需要时调用。
   *
   * 错误处理：
   * - Token过期或无效：自动清除本地Token
   * - 网络错误：返回undefined，让调用方处理
   * - 权限错误：记录日志并返回undefined
   *
   * @returns Promise<UserProfileResponse | undefined> 用户信息或undefined
   */ const fetchUserInfo = async ()=>{
                    try {
                        // 调用用户服务获取当前用户的详细信息
                        return await _services.UserService.getUserProfile();
                    } catch (error) {
                        // 如果获取用户信息失败，可能是Token过期或无效
                        // 清除本地Token，让用户重新登录
                        _services.AuthService.clearToken();
                        return undefined;
                    }
                };
                /**
   * 获取团队信息的函数
   *
   * 从后端API获取当前选择团队的详细信息，包括团队名称、成员数量等。
   * 只有在Token包含团队信息时才会尝试获取，避免不必要的API调用。
   *
   * 团队状态检查：
   * - 检查Token中是否包含teamId
   * - 只有团队Token才会调用团队API
   * - 用户Token不会触发团队信息获取
   *
   * @returns Promise<TeamDetailResponse | undefined> 团队信息或undefined
   */ const fetchTeamInfo = async ()=>{
                    try {
                        // 检查当前Token是否包含团队信息
                        // 只有在用户已选择团队的情况下才尝试获取团队详情
                        if (!(0, _tokenUtils.hasTeamInCurrentToken)()) return undefined;
                        // 调用团队服务获取当前团队的详细信息
                        return await _services.TeamService.getCurrentTeamDetail();
                    } catch (error) {
                        // 团队信息获取失败不影响用户基本功能
                        // 返回undefined，让组件显示无团队状态
                        return undefined;
                    }
                };
                /**
   * 路径检查和初始化逻辑
   *
   * 根据当前访问的路径决定是否需要进行身份验证初始化。
   * 对于公开页面（如登录、注册），跳过身份验证检查。
   * 对于需要身份验证的页面，执行完整的初始化流程。
   */ const { location } = _max.history;
                // 检查当前路径是否为公开页面（不需要身份验证）
                if (![
                    '/user/login',
                    '/user/register'
                ].includes(location.pathname)) try {
                    /**
       * 身份验证检查
       *
       * 首先检查用户是否已登录（本地是否有有效Token）。
       * 如果未登录，返回基础状态，让路由守卫处理跳转。
       */ if (!_services.AuthService.isLoggedIn()) // 用户未登录，返回基础状态
                    // 路由守卫会检测到currentUser为空并跳转到登录页
                    return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                    /**
       * 用户信息获取
       *
       * 用户已登录，尝试获取用户的详细信息。
       * 这一步会验证Token的有效性，如果Token过期会自动清除。
       */ const currentUser = await fetchUserInfo();
                    if (!currentUser) // 用户信息获取失败，可能是Token过期
                    // 返回基础状态，让路由守卫处理重新登录
                    return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                    /**
       * 团队信息获取
       *
       * 用户信息获取成功后，尝试获取团队信息。
       * 只有在Token包含团队信息时才会执行，避免不必要的API调用。
       * 团队信息获取失败不影响用户基本功能的使用。
       */ const currentTeam = await fetchTeamInfo();
                    /**
       * 返回完整的初始化状态
       *
       * 包含用户信息、团队信息（如果有）和数据获取函数。
       * 这些信息会被存储在全局状态中，供整个应用使用。
       */ return {
                        fetchUserInfo,
                        fetchTeamInfo,
                        currentUser,
                        currentTeam
                    };
                } catch (error) {
                    /**
       * 初始化异常处理
       *
       * 捕获初始化过程中的所有异常，确保应用能够正常启动。
       * 即使初始化失败，也要返回基础状态，让用户能够重新登录。
       */ return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                }
                /**
   * 公开页面的默认状态
   *
   * 对于登录、注册等公开页面，不需要进行身份验证检查。
   * 只返回数据获取函数，供页面在需要时使用。
   */ return {
                    fetchUserInfo,
                    fetchTeamInfo
                };
            }
            const layout = ({ initialState })=>{
                var _initialState_currentUser;
                return {
                    /**
     * 水印配置
     *
     * 在页面上显示用户名水印，用于标识当前登录用户。
     * 只有在用户已登录时才显示水印。
     */ waterMarkProps: {
                        content: initialState === null || initialState === void 0 ? void 0 : (_initialState_currentUser = initialState.currentUser) === null || _initialState_currentUser === void 0 ? void 0 : _initialState_currentUser.name
                    },
                    /**
     * 底部版权信息
     *
     * 渲染应用底部的版权信息和相关链接。
     */ footerRender: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/app.tsx",
                            lineNumber: 240,
                            columnNumber: 25
                        }, this),
                    /**
     * 右侧内容渲染
     *
     * 移除默认的右侧内容（如用户头像、设置等），
     * 使用自定义的FloatButton组件替代。
     */ rightContentRender: false,
                    /**
     * 页面切换时的路由守卫
     *
     * 这是应用级别的路由守卫，在每次页面切换时执行身份验证检查。
     * 确保只有已登录的用户才能访问需要身份验证的页面。
     *
     * 守卫逻辑：
     * 1. 检查全局状态中是否有用户信息
     * 2. 检查当前路径是否为公开页面
     * 3. 未登录用户访问受保护页面时自动跳转到登录页
     *
     * 公开页面列表：
     * - /user/login：用户登录页面
     * - /user/register：用户注册页面
     */ onPageChange: ()=>{
                        const { location } = _max.history;
                        // 检查用户是否已登录且当前页面是否需要身份验证
                        if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) && ![
                            '/user/login',
                            '/user/register'
                        ].includes(location.pathname)) // 用户未登录且访问受保护页面，跳转到登录页
                        _max.history.push('/user/login');
                    },
                    /**
     * 背景图片配置
     *
     * 自定义布局区域的背景装饰图片，用于美化页面视觉效果。
     * 这些图片会作为背景元素显示在页面的不同位置。
     */ bgLayoutImgList: [
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
                            left: 85,
                            bottom: 100,
                            height: '303px'
                        },
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
                            bottom: -68,
                            right: -45,
                            height: '303px'
                        },
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
                            bottom: 0,
                            left: 0,
                            width: '331px'
                        }
                    ],
                    /**
     * 自定义链接配置
     *
     * 页面底部或侧边栏的自定义链接列表。
     * 当前为空数组，表示不显示额外的链接。
     */ links: [],
                    /**
     * 菜单头部渲染
     *
     * 自定义侧边栏菜单的头部内容。
     * 设置为undefined表示使用默认的菜单头部。
     */ menuHeaderRender: undefined,
                    /**
     * 自定义403权限不足页面
     *
     * 当用户访问没有权限的页面时显示的内容。
     * 当前被注释掉，使用系统默认的403页面。
     */ // unAccessible: <div>unAccessible</div>,
                    /**
     * 子组件渲染器
     *
     * 这是整个应用的根组件包装器，为所有页面提供全局功能。
     * 包含错误边界、消息提供者和全局浮动按钮等功能。
     *
     * 全局组件说明：
     * - ErrorBoundary：捕获和处理React组件错误
     * - MessageProvider：提供全局消息通知功能
     * - UserFloatButton：用户操作的浮动按钮（登录后显示）
     *
     * 这些组件确保了应用的稳定性和用户体验的一致性。
     */ childrenRender: (children)=>{
                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.ErrorBoundary, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.MessageProvider, {
                                children: [
                                    children,
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                                        fileName: "src/app.tsx",
                                        lineNumber: 347,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/app.tsx",
                                lineNumber: 344,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/app.tsx",
                            lineNumber: 343,
                            columnNumber: 9
                        }, this);
                    }
                };
            };
            const request = {
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '6605424396301513257';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/team-management/index.tsx": [
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.8447053233131431360.hot-update.js.map