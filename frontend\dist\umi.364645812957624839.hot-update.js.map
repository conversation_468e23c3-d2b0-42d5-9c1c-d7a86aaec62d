{"version": 3, "sources": ["umi.364645812957624839.hot-update.js", "src/pages/subscription/components/SubscriptionPlansContent.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='1559473121463508828';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 订阅套餐内容组件\n */\n\nimport {\n  CheckOutlined,\n  CrownOutlined,\n  ShoppingCartOutlined,\n  StarOutlined,\n} from '@ant-design/icons';\nimport {\n  Button,\n  Card,\n  Col,\n  Divider,\n  InputNumber,\n  List,\n  Modal,\n  message,\n  Row,\n  Tag,\n  Typography,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport { SubscriptionService } from '@/services';\nimport type {\n  CreateSubscriptionRequest,\n  SubscriptionPlanResponse,\n} from '@/types/api';\n\nconst { Title, Text } = Typography;\n\ninterface SubscriptionPlansContentProps {\n  onSubscriptionSuccess?: () => void;\n}\n\nconst SubscriptionPlansContent: React.FC<SubscriptionPlansContentProps> = ({\n  onSubscriptionSuccess,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [plans, setPlans] = useState<SubscriptionPlanResponse[]>([]);\n  const [subscribing, setSubscribing] = useState(false);\n  const [selectedPlan, setSelectedPlan] =\n    useState<SubscriptionPlanResponse | null>(null);\n  const [subscribeModalVisible, setSubscribeModalVisible] = useState(false);\n  const [duration, setDuration] = useState(1);\n\n  useEffect(() => {\n    fetchPlans();\n  }, []);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const planList = await SubscriptionService.getActivePlans();\n      setPlans(planList);\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubscribe = (plan: SubscriptionPlanResponse) => {\n    setSelectedPlan(plan);\n    setDuration(1);\n    setSubscribeModalVisible(true);\n  };\n\n  const handleConfirmSubscribe = async () => {\n    if (!selectedPlan) return;\n\n    try {\n      setSubscribing(true);\n      const request: CreateSubscriptionRequest = {\n        planId: selectedPlan.id,\n        duration,\n      };\n\n      await SubscriptionService.createSubscription(request);\n      setSubscribeModalVisible(false);\n      message.success('订阅成功！');\n      onSubscriptionSuccess?.();\n    } catch (error) {\n      console.error('订阅失败:', error);\n    } finally {\n      setSubscribing(false);\n    }\n  };\n\n  const getPlanFeatures = (plan: SubscriptionPlanResponse) => {\n    const features = [\n      `数据存储上限：${plan.maxSize}GB`,\n      '7x24小时技术支持',\n      '数据备份与恢复',\n      '团队协作功能',\n    ];\n\n    if (plan.price > 0) {\n      features.push('高级分析报告');\n      features.push('API 访问权限');\n    }\n\n    if (plan.price >= 100) {\n      features.push('专属客户经理');\n      features.push('定制化功能');\n      features.push('优先技术支持');\n    }\n\n    return features;\n  };\n\n  const getPlanColor = (plan: SubscriptionPlanResponse) => {\n    if (plan.price === 0) return '#52c41a'; // 免费版 - 绿色\n    if (plan.price < 100) return '#1890ff'; // 基础版 - 蓝色\n    return '#722ed1'; // 专业版 - 紫色\n  };\n\n  const getPlanIcon = (plan: SubscriptionPlanResponse) => {\n    if (plan.price === 0) return <CheckOutlined />;\n    if (plan.price < 100) return <StarOutlined />;\n    return <CrownOutlined />;\n  };\n\n  const calculatePrice = (plan: SubscriptionPlanResponse, months: number) => {\n    return SubscriptionService.calculatePlanPrice(plan, months);\n  };\n\n  return (\n    <div>\n      <Row gutter={[24, 24]}>\n        {plans.map((plan) => {\n          const features = getPlanFeatures(plan);\n          const color = getPlanColor(plan);\n          const icon = getPlanIcon(plan);\n          const isPopular = plan.price > 0 && plan.price < 100;\n\n          return (\n            <Col xs={24} sm={12} lg={8} key={plan.id}>\n              <Card\n                hoverable\n                loading={loading}\n                style={{\n                  height: '100%',\n                  borderColor: isPopular ? '#1890ff' : undefined,\n                  position: 'relative',\n                }}\n              >\n                {isPopular && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: -1,\n                      right: 24,\n                      background: '#1890ff',\n                      color: 'white',\n                      padding: '4px 12px',\n                      borderRadius: '0 0 8px 8px',\n                      fontSize: 12,\n                      fontWeight: 'bold',\n                    }}\n                  >\n                    推荐\n                  </div>\n                )}\n\n                <div style={{ textAlign: 'center', marginBottom: 24 }}>\n                  <div style={{ fontSize: 48, color, marginBottom: 16 }}>\n                    {icon}\n                  </div>\n                  <Title level={3} style={{ margin: 0, color }}>\n                    {plan.name}\n                  </Title>\n                  <Text type=\"secondary\">{plan.description}</Text>\n                </div>\n\n                <div style={{ textAlign: 'center', marginBottom: 24 }}>\n                  <div style={{ fontSize: 36, fontWeight: 'bold', color }}>\n                    ¥{plan.price}\n                    <span style={{ fontSize: 16, fontWeight: 'normal' }}>\n                      /月\n                    </span>\n                  </div>\n                  {plan.price === 0 && (\n                    <Tag color=\"green\" style={{ marginTop: 8 }}>\n                      永久免费\n                    </Tag>\n                  )}\n                </div>\n\n                <List\n                  size=\"small\"\n                  dataSource={features}\n                  renderItem={(feature) => (\n                    <List.Item>\n                      <CheckOutlined\n                        style={{ color: '#52c41a', marginRight: 8 }}\n                      />\n                      {feature}\n                    </List.Item>\n                  )}\n                  style={{ marginBottom: 24 }}\n                />\n\n                <Button\n                  type={isPopular ? 'primary' : 'default'}\n                  size=\"large\"\n                  block\n                  icon={<ShoppingCartOutlined />}\n                  onClick={() => handleSubscribe(plan)}\n                  disabled={plan.price === 0} // 免费套餐不需要订阅\n                >\n                  {plan.price === 0 ? '当前套餐' : '立即订阅'}\n                </Button>\n              </Card>\n            </Col>\n          );\n        })}\n      </Row>\n\n      {/* 套餐对比 */}\n      <Card title=\"套餐对比\" style={{ marginTop: 32 }}>\n        <div style={{ overflowX: 'auto' }}>\n          {plans.length > 0 && (\n            <div>\n              {SubscriptionService.comparePlans(plans).map(\n                (comparison, index) => (\n                  <Row\n                    key={`comparison-${index}`}\n                    style={{\n                      padding: '12px 0',\n                      borderBottom: '1px solid #f0f0f0',\n                    }}\n                  >\n                    <Col span={6}>\n                      <Text strong>{comparison.feature}</Text>\n                    </Col>\n                    {comparison.values.map((value, valueIndex) => (\n                      <Col span={6} key={`value-${index}-${valueIndex}`}>\n                        <Text>\n                          {typeof value === 'boolean'\n                            ? value\n                              ? '✓'\n                              : '✗'\n                            : value}\n                        </Text>\n                      </Col>\n                    ))}\n                  </Row>\n                ),\n              )}\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* 订阅确认模态框 */}\n      <Modal\n        title=\"确认订阅\"\n        open={subscribeModalVisible}\n        onCancel={() => setSubscribeModalVisible(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setSubscribeModalVisible(false)}>\n            取消\n          </Button>,\n          <Button\n            key=\"confirm\"\n            type=\"primary\"\n            loading={subscribing}\n            onClick={handleConfirmSubscribe}\n          >\n            确认订阅\n          </Button>,\n        ]}\n      >\n        {selectedPlan && (\n          <div>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>套餐：</Text>\n              <Text>{selectedPlan.name}</Text>\n            </div>\n\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>订阅时长：</Text>\n              <InputNumber\n                min={1}\n                max={24}\n                value={duration}\n                onChange={(value) => setDuration(value || 1)}\n                addonAfter=\"个月\"\n                style={{ marginLeft: 8 }}\n              />\n            </div>\n\n            <Divider />\n\n            <div>\n              <Text strong>价格详情：</Text>\n              {(() => {\n                const priceInfo = calculatePrice(selectedPlan, duration);\n                return (\n                  <div style={{ marginTop: 8 }}>\n                    <div>原价：¥{priceInfo.originalPrice}</div>\n                    {priceInfo.discount > 0 && (\n                      <div style={{ color: '#ff4d4f' }}>\n                        折扣：-{priceInfo.discount}%\n                      </div>\n                    )}\n                    <div\n                      style={{\n                        fontSize: 18,\n                        fontWeight: 'bold',\n                        color: '#1890ff',\n                      }}\n                    >\n                      总计：¥{priceInfo.totalPrice}\n                    </div>\n                  </div>\n                );\n              })()}\n            </div>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default SubscriptionPlansContent;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;wCCqUb;;;2BAAA;;;;;;0CA/TO;yCAaA;oFACoC;6CACP;;;;;;;;;;YAMpC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAMlC,MAAM,2BAAoE,CAAC,EACzE,qBAAqB,EACtB;;gBACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAA6B,EAAE;gBACjE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;gBAC/C,MAAM,CAAC,cAAc,gBAAgB,GACnC,IAAA,eAAQ,EAAkC;gBAC5C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;gBACnE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;gBAEzC,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG,EAAE;gBAEL,MAAM,aAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,6BAAmB,CAAC,cAAc;wBACzD,SAAS;oBACX,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,MAAM,kBAAkB,CAAC;oBACvB,gBAAgB;oBAChB,YAAY;oBACZ,yBAAyB;gBAC3B;gBAEA,MAAM,yBAAyB;oBAC7B,IAAI,CAAC,cAAc;oBAEnB,IAAI;wBACF,eAAe;wBACf,MAAM,UAAqC;4BACzC,QAAQ,aAAa,EAAE;4BACvB;wBACF;wBAEA,MAAM,6BAAmB,CAAC,kBAAkB,CAAC;wBAC7C,yBAAyB;wBACzB,aAAO,CAAC,OAAO,CAAC;wBAChB,kCAAA,oCAAA;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,SAAS;oBACzB,SAAU;wBACR,eAAe;oBACjB;gBACF;gBAEA,MAAM,kBAAkB,CAAC;oBACvB,MAAM,WAAW;wBACf,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC;wBAC1B;wBACA;wBACA;qBACD;oBAED,IAAI,KAAK,KAAK,GAAG,GAAG;wBAClB,SAAS,IAAI,CAAC;wBACd,SAAS,IAAI,CAAC;oBAChB;oBAEA,IAAI,KAAK,KAAK,IAAI,KAAK;wBACrB,SAAS,IAAI,CAAC;wBACd,SAAS,IAAI,CAAC;wBACd,SAAS,IAAI,CAAC;oBAChB;oBAEA,OAAO;gBACT;gBAEA,MAAM,eAAe,CAAC;oBACpB,IAAI,KAAK,KAAK,KAAK,GAAG,OAAO,WAAW,WAAW;oBACnD,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,WAAW,WAAW;oBACnD,OAAO,WAAW,WAAW;gBAC/B;gBAEA,MAAM,cAAc,CAAC;oBACnB,IAAI,KAAK,KAAK,KAAK,GAAG,qBAAO,2BAAC,oBAAa;;;;;oBAC3C,IAAI,KAAK,KAAK,GAAG,KAAK,qBAAO,2BAAC,mBAAY;;;;;oBAC1C,qBAAO,2BAAC,oBAAa;;;;;gBACvB;gBAEA,MAAM,iBAAiB,CAAC,MAAgC;oBACtD,OAAO,6BAAmB,CAAC,kBAAkB,CAAC,MAAM;gBACtD;gBAEA,qBACE,2BAAC;;sCACC,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAG;sCAClB,MAAM,GAAG,CAAC,CAAC;gCACV,MAAM,WAAW,gBAAgB;gCACjC,MAAM,QAAQ,aAAa;gCAC3B,MAAM,OAAO,YAAY;gCACzB,MAAM,YAAY,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG;gCAEjD,qBACE,2BAAC,SAAG;oCAAC,IAAI;oCAAI,IAAI;oCAAI,IAAI;8CACvB,cAAA,2BAAC,UAAI;wCACH,SAAS;wCACT,SAAS;wCACT,OAAO;4CACL,QAAQ;4CACR,aAAa,YAAY,YAAY;4CACrC,UAAU;wCACZ;;4CAEC,2BACC,2BAAC;gDACC,OAAO;oDACL,UAAU;oDACV,KAAK;oDACL,OAAO;oDACP,YAAY;oDACZ,OAAO;oDACP,SAAS;oDACT,cAAc;oDACd,UAAU;oDACV,YAAY;gDACd;0DACD;;;;;;0DAKH,2BAAC;gDAAI,OAAO;oDAAE,WAAW;oDAAU,cAAc;gDAAG;;kEAClD,2BAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAI;4DAAO,cAAc;wDAAG;kEACjD;;;;;;kEAEH,2BAAC;wDAAM,OAAO;wDAAG,OAAO;4DAAE,QAAQ;4DAAG;wDAAM;kEACxC,KAAK,IAAI;;;;;;kEAEZ,2BAAC;wDAAK,MAAK;kEAAa,KAAK,WAAW;;;;;;;;;;;;0DAG1C,2BAAC;gDAAI,OAAO;oDAAE,WAAW;oDAAU,cAAc;gDAAG;;kEAClD,2BAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAI,YAAY;4DAAQ;wDAAM;;4DAAG;4DACrD,KAAK,KAAK;0EACZ,2BAAC;gEAAK,OAAO;oEAAE,UAAU;oEAAI,YAAY;gEAAS;0EAAG;;;;;;;;;;;;oDAItD,KAAK,KAAK,KAAK,mBACd,2BAAC,SAAG;wDAAC,OAAM;wDAAQ,OAAO;4DAAE,WAAW;wDAAE;kEAAG;;;;;;;;;;;;0DAMhD,2BAAC,UAAI;gDACH,MAAK;gDACL,YAAY;gDACZ,YAAY,CAAC,wBACX,2BAAC,UAAI,CAAC,IAAI;;0EACR,2BAAC,oBAAa;gEACZ,OAAO;oEAAE,OAAO;oEAAW,aAAa;gEAAE;;;;;;4DAE3C;;;;;;;gDAGL,OAAO;oDAAE,cAAc;gDAAG;;;;;;0DAG5B,2BAAC,YAAM;gDACL,MAAM,YAAY,YAAY;gDAC9B,MAAK;gDACL,KAAK;gDACL,oBAAM,2BAAC,2BAAoB;;;;;gDAC3B,SAAS,IAAM,gBAAgB;gDAC/B,UAAU,KAAK,KAAK,KAAK;0DAExB,KAAK,KAAK,KAAK,IAAI,SAAS;;;;;;;;;;;;mCA1EF,KAAK,EAAE;;;;;4BA+E5C;;;;;;sCAIF,2BAAC,UAAI;4BAAC,OAAM;4BAAO,OAAO;gCAAE,WAAW;4BAAG;sCACxC,cAAA,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAO;0CAC7B,MAAM,MAAM,GAAG,mBACd,2BAAC;8CACE,6BAAmB,CAAC,YAAY,CAAC,OAAO,GAAG,CAC1C,CAAC,YAAY,sBACX,2BAAC,SAAG;4CAEF,OAAO;gDACL,SAAS;gDACT,cAAc;4CAChB;;8DAEA,2BAAC,SAAG;oDAAC,MAAM;8DACT,cAAA,2BAAC;wDAAK,MAAM;kEAAE,WAAW,OAAO;;;;;;;;;;;gDAEjC,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC7B,2BAAC,SAAG;wDAAC,MAAM;kEACT,cAAA,2BAAC;sEACE,OAAO,UAAU,YACd,QACE,MACA,MACF;;;;;;uDANW,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC;;;;;;2CAV9C,CAAC,WAAW,EAAE,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;sCA6BxC,2BAAC,WAAK;4BACJ,OAAM;4BACN,MAAM;4BACN,UAAU,IAAM,yBAAyB;4BACzC,QAAQ;8CACN,2BAAC,YAAM;oCAAc,SAAS,IAAM,yBAAyB;8CAAQ;mCAAzD;;;;;8CAGZ,2BAAC,YAAM;oCAEL,MAAK;oCACL,SAAS;oCACT,SAAS;8CACV;mCAJK;;;;;6BAOP;sCAEA,8BACC,2BAAC;;kDACC,2BAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAG;;0DAC7B,2BAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,2BAAC;0DAAM,aAAa,IAAI;;;;;;;;;;;;kDAG1B,2BAAC;wCAAI,OAAO;4CAAE,cAAc;wCAAG;;0DAC7B,2BAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,2BAAC,iBAAW;gDACV,KAAK;gDACL,KAAK;gDACL,OAAO;gDACP,UAAU,CAAC,QAAU,YAAY,SAAS;gDAC1C,YAAW;gDACX,OAAO;oDAAE,YAAY;gDAAE;;;;;;;;;;;;kDAI3B,2BAAC,aAAO;;;;;kDAER,2BAAC;;0DACC,2BAAC;gDAAK,MAAM;0DAAC;;;;;;4CACX,CAAA;gDACA,MAAM,YAAY,eAAe,cAAc;gDAC/C,qBACE,2BAAC;oDAAI,OAAO;wDAAE,WAAW;oDAAE;;sEACzB,2BAAC;;gEAAI;gEAAK,UAAU,aAAa;;;;;;;wDAChC,UAAU,QAAQ,GAAG,mBACpB,2BAAC;4DAAI,OAAO;gEAAE,OAAO;4DAAU;;gEAAG;gEAC3B,UAAU,QAAQ;gEAAC;;;;;;;sEAG5B,2BAAC;4DACC,OAAO;gEACL,UAAU;gEACV,YAAY;gEACZ,OAAO;4DACT;;gEACD;gEACM,UAAU,UAAU;;;;;;;;;;;;;4CAIjC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;YAOd;eAlSM;iBAAA;gBAoSN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDrUD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC7kB"}